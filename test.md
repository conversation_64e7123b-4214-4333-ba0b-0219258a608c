# 快速排序实现与测试

本文档记录了在 `test.py` 文件中实现快速排序算法、与现有的堆排序进行比较，以及整个调试过程的详细步骤。

## 1. 初始需求

用户的要求是在一个已实现堆排序的 `test.py` 文件中，添加快速排序的实现，并更新测试框架以对两种算法进行比较。

## 2. 实现规划

我制定了以下计划来完成此任务：

1.  **添加快速排序函数**：创建一个 `quick_sort` 函数，并实现其核心的分区和递归逻辑。
2.  **更新测试框架**：
    *   重构 `enhanced_benchmark` 函数，使其能接受一个排序函数作为参数，从而可以对任何排序算法进行基准测试。
    *   更新 `run_comprehensive_tests` 和 `run_performance_tests`，以同时测试和比较堆排序与快速排序。
3.  **更新文档**：修改文件顶部的文档字符串，以反映新增的快速排序算法。

## 3. 实现与调试过程

在实现过程中，我遵循了上述计划。首先，我添加了 `quick_sort` 函数及其辅助函数。接着，我重构了测试函数，使其更加通用。

然而，在第一次尝试运行更新后的脚本时，我遇到了一个 `SyntaxError`:

```
File "/Users/<USER>/Downloads/AICode/test.py", line 270
    print(f"

           ^
SyntaxError: EOL while scanning string literal
```

经过检查，我发现问题是由几个 f-string 中的非法换行符 `
` 引起的。`print` 函数会自动添加换行符，因此字符串开头的 `
` 是多余的，并且导致了语法错误。

我通过一次 `MultiEdit` 操作，移除了所有 `print` 语句中多余的 `
`，成功修复了此错误。

## 4. 最终 `test.py` 代码

```python
"""
排序算法实现 (Sorting Algorithms Implementation)

该文件实现了两种经典的排序算法：堆排序和快速排序，并提供了一个全面的测试框架。

算法特性：
- 堆排序 (Heap Sort):
  - 时间复杂度：O(n log n)
  - 空间复杂度：O(1)
  - 不稳定排序
- 快速排序 (Quick Sort):
  - 平均时间复杂度：O(n log n)
  - 最坏情况时间复杂度：O(n^2)
  - 空间复杂度：O(log n) (递归栈)
  - 不稳定排序

优化与功能：
- 对大数据集使用迭代版堆化，避免递归栈溢出
- 增强的错误处理，支持浮点数和负数
- 模块化的测试框架，可轻松扩展以测试其他排序算法
- 性能基准测试，用于比较不同算法的效率

作者: AI Assistant
版本: 3.0 (Multi-algorithm)
"""

from typing import List, Optional, Union, Tuple, Callable
import time
import random
import sys
from dataclasses import dataclass
from enum import Enum


@dataclass
class SortResult:
    """排序结果数据类"""
    sorted_array: List[Union[int, float]]
    execution_time: float
    is_valid: bool


class TestStatus(Enum):
    """测试状态枚举"""
    PASSED = "✅ 通过"
    FAILED = "❌ 失败"
    ERROR = "⚠️ 错误"


def heapify_iterative(arr: List[Union[int, float]], heap_size: int, root_index: int) -> None:
    """使用迭代方法进行堆化（避免递归栈溢出）"""
    while True:
        largest_index = root_index
        left_child = 2 * root_index + 1
        right_child = 2 * root_index + 2

        if left_child < heap_size and arr[left_child] > arr[largest_index]:
            largest_index = left_child

        if right_child < heap_size and arr[right_child] > arr[largest_index]:
            largest_index = right_child

        if largest_index == root_index:
            break
            
        arr[root_index], arr[largest_index] = arr[largest_index], arr[root_index]
        root_index = largest_index


def heapify(arr: List[Union[int, float]], heap_size: int, root_index: int) -> None:
    """
    将以root_index为根的子树调整为最大堆
    
    Args:
        arr: 待调整的数组
        heap_size: 堆的大小
        root_index: 根节点索引
    """
    if heap_size > 1000:  # 对大数据使用迭代版本避免栈溢出
        heapify_iterative(arr, heap_size, root_index)
        return
        
    largest_index = root_index
    left_child = 2 * root_index + 1
    right_child = 2 * root_index + 2

    if left_child < heap_size and arr[left_child] > arr[largest_index]:
        largest_index = left_child

    if right_child < heap_size and arr[right_child] > arr[largest_index]:
        largest_index = right_child

    if largest_index != root_index:
        arr[root_index], arr[largest_index] = arr[largest_index], arr[root_index]
        heapify(arr, heap_size, largest_index)


def heap_sort(arr: List[Union[int, float]]) -> Optional[List[Union[int, float]]]:
    """
    使用堆排序算法对数组进行升序排序
    
    Args:
        arr: 待排序的整数数组
        
    Returns:
        排序后的数组，如果输入无效则返回None
    """
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")
    
    if not arr:
        return arr
    
    if not all(isinstance(x, (int, float)) for x in arr):
        raise ValueError("数组中包含非数字元素")
    
    if any(x != x for x in arr):  # 检查NaN
        raise ValueError("数组中包含NaN值")
    
    if len(arr) > sys.maxsize // 2:  # 检查内存限制
        raise MemoryError("数组太大，超出内存限制")
    
    n = len(arr)
    
    # 构建最大堆（从最后一个非叶子节点开始）
    for i in range(n // 2 - 1, -1, -1):
        heapify(arr, n, i)
    
    # 逐个提取堆顶元素并重新调整堆
    for i in range(n - 1, 0, -1):
        # 将堆顶元素（最大值）移到数组末尾
        arr[0], arr[i] = arr[i], arr[0]
        # 重新调整堆（堆大小减1）
        heapify(arr, i, 0)
    
    return arr


def partition(arr: List[Union[int, float]], low: int, high: int) -> int:
    """
    分区函数，用于快速排序

    Args:
        arr: 待分区的数组
        low: 起始索引
        high: 结束索引

    Returns:
        基准元素的最终位置
    """
    pivot = arr[high]
    i = low - 1
    for j in range(low, high):
        if arr[j] <= pivot:
            i += 1
            arr[i], arr[j] = arr[j], arr[i]
    arr[i + 1], arr[high] = arr[high], arr[i + 1]
    return i + 1


def quick_sort_recursive(arr: List[Union[int, float]], low: int, high: int) -> None:
    """
    快速排序的递归辅助函数

    Args:
        arr: 待排序的数组
        low: 起始索引
        high: 结束索引
    """
    if low < high:
        pi = partition(arr, low, high)
        quick_sort_recursive(arr, low, pi - 1)
        quick_sort_recursive(arr, pi + 1, high)


def quick_sort(arr: List[Union[int, float]]) -> Optional[List[Union[int, float]]]:
    """
    使用快速排序算法对数组进行升序排序

    Args:
        arr: 待排序的数组

    Returns:
        排序后的数组，如果输入无效则返回None
    """
    if not isinstance(arr, list):
        raise TypeError("输入必须是列表类型")
    
    if not arr:
        return arr
    
    if not all(isinstance(x, (int, float)) for x in arr):
        raise ValueError("数组中包含非数字元素")
    
    if any(x != x for x in arr):  # 检查NaN
        raise ValueError("数组中包含NaN值")
    
    if len(arr) > sys.maxsize // 2:
        raise MemoryError("数组太大，超出内存限制")
        
    quick_sort_recursive(arr, 0, len(arr) - 1)
    return arr


def validate_sorting(original: List[Union[int, float]], sorted_arr: List[Union[int, float]]) -> bool:
    """
    验证排序结果的正确性
    
    Args:
        original: 原始数组
        sorted_arr: 排序后的数组
        
    Returns:
        排序是否正确
    """
    if len(original) != len(sorted_arr):
        return False
    
    # 检查是否为升序
    for i in range(1, len(sorted_arr)):
        if sorted_arr[i] < sorted_arr[i-1]:
            return False
    
    # 检查元素是否完整（通过排序验证）
    return sorted(original) == sorted_arr


def enhanced_benchmark(sort_function: Callable, arr: List[Union[int, float]], iterations: int = 3) -> Tuple[float, float, float]:
    """
    增强版性能测试，返回平均时间、最小时间和最大时间
    """
    times = []
    for _ in range(iterations):
        test_arr = arr.copy()
        start_time = time.perf_counter()
        sort_function(test_arr)
        end_time = time.perf_counter()
        times.append(end_time - start_time)
    
    return sum(times) / len(times), min(times), max(times)


def run_comprehensive_tests() -> None:
    """运行综合测试套件"""
    test_cases = [
        ([], "空数组测试"),
        ([1], "单元素测试"),
        ([12, 11, 13, 5, 6, 7], "基本测试"),
        ([3, 1, 4, 1, 5, 9, 2, 6], "重复元素测试"),
        ([9, 8, 7, 6, 5, 4, 3, 2, 1], "逆序数组测试"),
        ([1, 2, 3, 4, 5, 6, 7, 8, 9], "已排序数组测试"),
        ([-5, -1, -10, 0, 3], "包含负数测试"),
        ([1.5, 2.7, 0.3, -1.2], "浮点数测试"),
        ([42] * 100, "相同元素数组测试"),
        ([random.randint(-1000, 1000) for _ in range(50)], "随机数据测试")
    ]
    
    sorting_algorithms = {
        "堆排序": heap_sort,
        "快速排序": quick_sort
    }

    for name, sort_function in sorting_algorithms.items():
        print(f"=== {name}算法综合测试 ===")
        success_count = 0
        total_cases = len(test_cases)
        
        for i, (test_arr, description) in enumerate(test_cases, 1):
            original_arr = test_arr.copy()
            print(f"测试用例 {i}: {description}")
            
            try:
                if len(test_arr) <= 20:
                    print(f"  原始数组: {original_arr}")
                else:
                    print(f"  数组大小: {len(original_arr)}")
                
                avg_time, min_time, max_time = enhanced_benchmark(sort_function, test_arr.copy())
                sorted_arr = sort_function(test_arr.copy())
                
                if validate_sorting(original_arr, sorted_arr):
                    status = TestStatus.PASSED
                    success_count += 1
                else:
                    status = TestStatus.FAILED
                    
                if len(sorted_arr) <= 20:
                    print(f"  排序结果: {sorted_arr}")
                print(f"  验证结果: {status.value}")
                print(f"  平均时间: {avg_time:.6f}秒 (最小: {min_time:.6f}, 最大: {max_time:.6f})")
                
            except Exception as e:
                print(f"  错误: {e}")
                print(f"  状态: {TestStatus.ERROR.value}")
        
        print(f"=== {name} 测试结果统计 ===")
        print(f"成功率: {success_count}/{total_cases} ({success_count/total_cases*100:.1f}%)")


def run_performance_tests() -> None:
    """运行性能测试"""
    print("=== 性能测试 ===")
    test_sizes = [100, 500, 1000, 2000, 5000, 10000]
    
    sorting_algorithms = {
        "堆排序": heap_sort,
        "快速排序": quick_sort
    }

    for size in test_sizes:
        print(f"--- 数组大小: {size} ---")
        test_data = [random.randint(-1000, 1000) for _ in range(size)]
        
        for name, sort_function in sorting_algorithms.items():
            avg_time, _, _ = enhanced_benchmark(sort_function, test_data.copy(), iterations=5)
            items_per_second = size / avg_time if avg_time > 0 else float('inf')
            print(f"{name:8s}: 平均 {avg_time:.6f}秒, 速度: {items_per_second:8.0f} 元素/秒")


def main():
    """主函数：运行所有测试"""
    try:
        run_comprehensive_tests()
        run_performance_tests()
    except KeyboardInterrupt:
        print("测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")


if __name__ == '__main__':
    main()
```

## 5. 最终测试结果

```
=== 堆排序算法综合测试 ===
测试用例 1: 空数组测试
  原始数组: []
  排序结果: []
  验证结果: ✅ 通过
  平均时间: 0.000000秒 (最小: 0.000000, 最大: 0.000001)
测试用例 2: 单元素测试
  原始数组: [1]
  排序结果: [1]
  验证结果: ✅ 通过
  平均时间: 0.000001秒 (最小: 0.000001, 最大: 0.000002)
测试用例 3: 基本测试
  原始数组: [12, 11, 13, 5, 6, 7]
  排序结果: [5, 6, 7, 11, 12, 13]
  验证结果: ✅ 通过
  平均时间: 0.000003秒 (最小: 0.000003, 最大: 0.000004)
测试用例 4: 重复元素测试
  原始数组: [3, 1, 4, 1, 5, 9, 2, 6]
  排序结果: [1, 1, 2, 3, 4, 5, 6, 9]
  验证结果: ✅ 通过
  平均时间: 0.000005秒 (最小: 0.000004, 最大: 0.000005)
测试用例 5: 逆序数组测试
  原始数组: [9, 8, 7, 6, 5, 4, 3, 2, 1]
  排序结果: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  验证结果: ✅ 通过
  平均时间: 0.000005秒 (最小: 0.000005, 最大: 0.000005)
测试用例 6: 已排序数组测试
  原始数组: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  排序结果: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  验证结果: ✅ 通过
  平均时间: 0.000006秒 (最小: 0.000005, 最大: 0.000006)
测试用例 7: 包含负数测试
  原始数组: [-5, -1, -10, 0, 3]
  排序结果: [-10, -5, -1, 0, 3]
  验证结果: ✅ 通过
  平均时间: 0.000003秒 (最小: 0.000003, 最大: 0.000003)
测试用例 8: 浮点数测试
  原始数组: [1.5, 2.7, 0.3, -1.2]
  排序结果: [-1.2, 0.3, 1.5, 2.7]
  验证结果: ✅ 通过
  平均时间: 0.000002秒 (最小: 0.000002, 最大: 0.000002)
测试用例 9: 相同元素数组测试
  数组大小: 100
  验证结果: ✅ 通过
  平均时间: 0.000028秒 (最小: 0.000028, 最大: 0.000029)
测试用例 10: 随机数据测试
  数组大小: 50
  验证结果: ✅ 通过
  平均时间: 0.000049秒 (最小: 0.000043, 最大: 0.000059)
=== 堆排序 测试结果统计 ===
成功率: 10/10 (100.0%)
=== 快速排序算法综合测试 ===
测试用例 1: 空数组测试
  原始数组: []
  排序结果: []
  验证结果: ✅ 通过
  平均时间: 0.000000秒 (最小: 0.000000, 最大: 0.000000)
测试用例 2: 单元素测试
  原始数组: [1]
  排序结果: [1]
  验证结果: ✅ 通过
  平均时间: 0.000001秒 (最小: 0.000001, 最大: 0.000001)
测试用例 3: 基本测试
  原始数组: [12, 11, 13, 5, 6, 7]
  排序结果: [5, 6, 7, 11, 12, 13]
  验证结果: ✅ 通过
  平均时间: 0.000003秒 (最小: 0.000002, 最大: 0.000004)
测试用例 4: 重复元素测试
  原始数组: [3, 1, 4, 1, 5, 9, 2, 6]
  排序结果: [1, 1, 2, 3, 4, 5, 6, 9]
  验证结果: ✅ 通过
  平均时间: 0.000003秒 (最小: 0.000003, 最大: 0.000003)
测试用例 5: 逆序数组测试
  原始数组: [9, 8, 7, 6, 5, 4, 3, 2, 1]
  排序结果: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  验证结果: ✅ 通过
  平均时间: 0.000005秒 (最小: 0.000004, 最大: 0.000005)
测试用例 6: 已排序数组测试
  原始数组: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  排序结果: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  验证结果: ✅ 通过
  平均时间: 0.000005秒 (最小: 0.000005, 最大: 0.000005)
测试用例 7: 包含负数测试
  原始数组: [-5, -1, -10, 0, 3]
  排序结果: [-10, -5, -1, 0, 3]
  验证结果: ✅ 通过
  平均时间: 0.000003秒 (最小: 0.000002, 最大: 0.000003)
测试用例 8: 浮点数测试
  原始数组: [1.5, 2.7, 0.3, -1.2]
  排序结果: [-1.2, 0.3, 1.5, 2.7]
  验证结果: ✅ 通过
  平均时间: 0.000002秒 (最小: 0.000001, 最大: 0.000002)
测试用例 9: 相同元素数组测试
  数组大小: 100
  验证结果: ✅ 通过
  平均时间: 0.000259秒 (最小: 0.000254, 最大: 0.000266)
测试用例 10: 随机数据测试
  数组大小: 50
  验证结果: ✅ 通过
  平均时间: 0.000020秒 (最小: 0.000020, 最大: 0.000020)
=== 快速排序 测试结果统计 ===
成功率: 10/10 (100.0%)
=== 性能测试 ===
--- 数组大小: 100 ---
堆排序     : 平均 0.000095秒, 速度:  1048493 元素/秒
快速排序    : 平均 0.000048秒, 速度:  2085506 元素/秒
--- 数组大小: 500 ---
堆排序     : 平均 0.000657秒, 速度:   760745 元素/秒
快速排序    : 平均 0.000282秒, 速度:  1770537 元素/秒
--- 数组大小: 1000 ---
堆排序     : 平均 0.001538秒, 速度:   650336 元素/秒
快速排序    : 平均 0.000665秒, 速度:  1503873 元素/秒
--- 数组大小: 2000 ---
堆排序     : 平均 0.003094秒, 速度:   646341 元素/秒
快速排序    : 平均 0.001413秒, 速度:  1415445 元素/秒
--- 数组大小: 5000 ---
堆排序     : 平均 0.008435秒, 速度:   592740 元素/秒
快速排序    : 平均 0.003962秒, 速度:  1261986 元素/秒
--- 数组大小: 10000 ---
堆排序     : 平均 0.018074秒, 速度:   553280 元素/秒
快速排序    : 平均 0.009596秒, 速度:  1042097 元素/秒
```
---

## 堆排序实现与测试

### 1. `test.py` 代码

```python
def heapify(arr, n, i):
    largest = i
    l = 2 * i + 1
    r = 2 * i + 2

    if l < n and arr[i] < arr[l]:
        largest = l

    if r < n and arr[largest] < arr[r]:
        largest = r

    if largest != i:
        arr[i], arr[largest] = arr[largest], arr[i]
        heapify(arr, n, largest)

def heap_sort(arr):
    n = len(arr)

    for i in range(n // 2 - 1, -1, -1):
        heapify(arr, n, i)

    for i in range(n - 1, 0, -1):
        arr[i], arr[0] = arr[0], arr[i]
        heapify(arr, i, 0)

if __name__ == '__main__':
    # Test cases
    test_cases = [
        [12, 11, 13, 5, 6, 7],
        [],
        [1],
        [3, 1, 4, 1, 5, 9, 2, 6],
        [9, 8, 7, 6, 5, 4, 3, 2, 1],
        [1, 2, 3, 4, 5, 6, 7, 8, 9]
    ]

    for i, arr in enumerate(test_cases):
        original_arr = arr.copy()
        print(f"Test case {i+1}:")
        print(f"  Original array: {original_arr}")
        heap_sort(arr)
        print(f"  Sorted array:   {arr}")
        print("-" * 20)
```

### 2. 调试与测试过程

我使用了六个测试用例，覆盖了以下情况：
- **基本情况**：一个包含正整数的普通数组。
- **边界情况**：一个空数组和一个只包含一个元素的数组。
- **复杂情况**：包含重复元素的数组。
- **有序和逆序情况**：一个完全逆序的数组和一个已经有序的数组。

### 3. 测试结果

```
Test case 1:
  Original array: [12, 11, 13, 5, 6, 7]
  Sorted array:   [5, 6, 7, 11, 12, 13]
--------------------
Test case 2:
  Original array: []
  Sorted array:   []
--------------------
Test case 3:
  Original array: [1]
  Sorted array:   [1]
--------------------
Test case 4:
  Original array: [3, 1, 4, 1, 5, 9, 2, 6]
  Sorted array:   [1, 1, 2, 3, 4, 5, 6, 9]
--------------------
Test case 5:
  Original array: [9, 8, 7, 6, 5, 4, 3, 2, 1]
  Sorted array:   [1, 2, 3, 4, 5, 6, 7, 8, 9]
--------------------
Test case 6:
  Original array: [1, 2, 3, 4, 5, 6, 7, 8, 9]
  Sorted array:   [1, 2, 3, 4, 5, 6, 7, 8, 9]
--------------------
```

从输出结果可以看出，堆排序算法成功地对所有测试用例进行了正确的排序。
