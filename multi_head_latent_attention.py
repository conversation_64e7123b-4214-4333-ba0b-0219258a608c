import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class MultiHeadLatentAttention(nn.Module):
    def __init__(self, d_model, num_heads, num_latents, latent_dim=None, dropout=0.1):
        """
        Multi-head Latent Attention module
        
        Args:
            d_model: model dimension
            num_heads: number of attention heads
            num_latents: number of latent queries
            latent_dim: dimension of latent space (default: d_model)
            dropout: dropout rate
        """
        super().__init__()
        self.d_model = d_model
        self.num_heads = num_heads
        self.num_latents = num_latents
        self.latent_dim = latent_dim or d_model
        self.head_dim = self.latent_dim // num_heads
        
        assert self.latent_dim % num_heads == 0, "latent_dim must be divisible by num_heads"
        
        # Learnable latent queries
        self.latent_queries = nn.Parameter(torch.randn(num_latents, self.latent_dim))
        
        # Cross-attention layers (latent queries attend to input)
        self.cross_attn_q = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.cross_attn_k = nn.Linear(d_model, self.latent_dim, bias=False)
        self.cross_attn_v = nn.Linear(d_model, self.latent_dim, bias=False)
        
        # Self-attention layers (latent queries attend to each other)
        self.self_attn_q = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.self_attn_k = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        self.self_attn_v = nn.Linear(self.latent_dim, self.latent_dim, bias=False)
        
        # Output projection (latent space back to input space)
        self.output_proj = nn.Linear(self.latent_dim, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.scale = math.sqrt(self.head_dim)
        
        self._init_weights()
    
    def _init_weights(self):
        """Initialize weights using Xavier uniform"""
        for module in [self.cross_attn_q, self.cross_attn_k, self.cross_attn_v,
                      self.self_attn_q, self.self_attn_k, self.self_attn_v, self.output_proj]:
            nn.init.xavier_uniform_(module.weight)
        
        nn.init.xavier_uniform_(self.latent_queries)
    
    def _multi_head_attention(self, q, k, v, mask=None):
        """
        Multi-head attention computation
        
        Args:
            q: queries [batch_size, seq_len_q, latent_dim]
            k: keys [batch_size, seq_len_k, latent_dim] 
            v: values [batch_size, seq_len_v, latent_dim]
            mask: attention mask [batch_size, seq_len_q, seq_len_k]
        """
        batch_size = q.size(0)
        seq_len_q = q.size(1)
        seq_len_k = k.size(1)
        
        # Reshape for multi-head attention
        q = q.view(batch_size, seq_len_q, self.num_heads, self.head_dim).transpose(1, 2)
        k = k.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        v = v.view(batch_size, seq_len_k, self.num_heads, self.head_dim).transpose(1, 2)
        
        # Compute attention scores
        scores = torch.matmul(q, k.transpose(-2, -1)) / self.scale
        
        # Apply mask if provided
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Apply softmax and dropout
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # Apply attention to values
        out = torch.matmul(attn_weights, v)
        
        # Reshape back
        out = out.transpose(1, 2).contiguous().view(
            batch_size, seq_len_q, self.latent_dim
        )
        
        return out, attn_weights
    
    def forward(self, x, mask=None):
        """
        Forward pass
        
        Args:
            x: input tensor [batch_size, seq_len, d_model]
            mask: attention mask [batch_size, seq_len]
            
        Returns:
            output: [batch_size, seq_len, d_model]
            attn_weights: attention weights for visualization
        """
        batch_size, seq_len = x.size(0), x.size(1)
        
        # Expand latent queries for batch
        latents = self.latent_queries.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Cross-attention: latent queries attend to input
        cross_q = self.cross_attn_q(latents)
        cross_k = self.cross_attn_k(x)
        cross_v = self.cross_attn_v(x)
        
        # Create cross-attention mask if input mask is provided
        cross_mask = None
        if mask is not None:
            cross_mask = mask.unsqueeze(1).expand(-1, self.num_latents, -1)
        
        latents_attended, cross_attn_weights = self._multi_head_attention(
            cross_q, cross_k, cross_v, cross_mask
        )
        
        # Self-attention: latent queries attend to each other
        self_q = self.self_attn_q(latents_attended)
        self_k = self.self_attn_k(latents_attended)
        self_v = self.self_attn_v(latents_attended)
        
        latents_refined, self_attn_weights = self._multi_head_attention(
            self_q, self_k, self_v
        )
        
        # Project latents back to attend to input
        output_q = latents_refined
        output_k = self.cross_attn_k(x)  # Reuse cross-attention keys
        output_v = x  # Direct attention to input values
        
        # Final attention: refined latents attend to input to produce output
        final_attn_weights = torch.matmul(
            F.softmax(torch.matmul(output_q, output_k.transpose(-2, -1)) / self.scale, dim=-1),
            output_v
        )
        
        # Average over latent dimensions and project to original space
        output = self.output_proj(final_attn_weights.mean(dim=1, keepdim=True).expand(-1, seq_len, -1))
        
        return output, {
            'cross_attn': cross_attn_weights,
            'self_attn': self_attn_weights
        }


# Example usage and testing
if __name__ == "__main__":
    # Model parameters
    batch_size = 2
    seq_len = 100
    d_model = 512
    num_heads = 8
    num_latents = 32
    
    # Create model
    model = MultiHeadLatentAttention(
        d_model=d_model,
        num_heads=num_heads,
        num_latents=num_latents,
        dropout=0.1
    )
    
    # Create sample input
    x = torch.randn(batch_size, seq_len, d_model)
    mask = torch.ones(batch_size, seq_len)  # No masking
    
    # Forward pass
    output, attn_weights = model(x, mask)
    
    print(f"Input shape: {x.shape}")
    print(f"Output shape: {output.shape}")
    print(f"Cross attention weights shape: {attn_weights['cross_attn'].shape}")
    print(f"Self attention weights shape: {attn_weights['self_attn'].shape}")
    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")